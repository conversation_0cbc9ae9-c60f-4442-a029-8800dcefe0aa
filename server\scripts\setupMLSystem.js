#!/usr/bin/env node

/**
 * ML System Setup Script
 * 
 * This script sets up the complete ML document classification system,
 * including database initialization, model setup, and sample data creation.
 */

const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
require('dotenv').config();

// Import ML modules
const documentClassification = require('../ai/documentClassification');
const trainingManager = require('../ai/trainingManager');
const trainingDataManager = require('../ai/trainingDataManager');
const modelEvaluator = require('../ai/modelEvaluator');

// Import models
const { DocumentClassification, TrainingSession, ModelPerformance } = require('../models/DocumentClassification');

/**
 * Setup configuration
 */
const SETUP_CONFIG = {
  createSampleData: true,
  initializeModels: true,
  setupDatabase: true,
  createDirectories: true,
  runTests: true
};

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/online-voting-system';
    
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('✅ Connected to MongoDB successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    return false;
  }
}

/**
 * Initialize database collections and indexes
 */
async function initializeDatabase() {
  try {
    console.log('🗄️  Initializing database collections...');
    
    // Create indexes for better performance
    await DocumentClassification.createIndexes();
    await TrainingSession.createIndexes();
    await ModelPerformance.createIndexes();
    
    console.log('✅ Database collections and indexes created');
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize database:', error.message);
    return false;
  }
}

/**
 * Initialize ML system components
 */
async function initializeMLSystem() {
  try {
    console.log('🤖 Initializing ML document classification system...');
    
    // Initialize document classification
    const success = await documentClassification.initializeDocumentClassification();
    
    if (success) {
      console.log('✅ Document classification system initialized');
      
      // Initialize directory structure
      trainingDataManager.initializeDirectoryStructure();
      console.log('✅ Training data directories created');
      
      return true;
    } else {
      console.log('❌ Failed to initialize document classification system');
      return false;
    }
  } catch (error) {
    console.error('❌ Error initializing ML system:', error.message);
    return false;
  }
}

/**
 * Create sample training data structure
 */
async function createSampleDataStructure() {
  try {
    console.log('📁 Creating sample data structure...');
    
    const sampleDataPath = path.join(__dirname, '../sample_data');
    const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);
    
    // Create main sample data directory
    if (!fs.existsSync(sampleDataPath)) {
      fs.mkdirSync(sampleDataPath, { recursive: true });
    }
    
    // Create subdirectories for each document type
    documentTypes.forEach(docType => {
      const docTypePath = path.join(sampleDataPath, docType);
      if (!fs.existsSync(docTypePath)) {
        fs.mkdirSync(docTypePath, { recursive: true });
      }
      
      // Create README file with instructions
      const readmePath = path.join(docTypePath, 'README.md');
      const readmeContent = `# ${docType.toUpperCase()} Training Data

Place your ${docType.replace('_', ' ')} document images in this directory.

## Guidelines:
- Use clear, high-quality images
- Ensure documents are complete and readable
- Supported formats: JPG, PNG, GIF, BMP
- Maximum file size: 10MB
- Minimum resolution: 224x224 pixels

## Naming Convention:
- Use descriptive filenames
- Example: aadhar_sample_001.jpg

## Quality Requirements:
- Good lighting and contrast
- Minimal blur or distortion
- Complete document visible
- Various orientations acceptable
`;
      
      fs.writeFileSync(readmePath, readmeContent);
    });
    
    // Create main README
    const mainReadmePath = path.join(sampleDataPath, 'README.md');
    const mainReadmeContent = `# ML Training Sample Data

This directory contains sample training data for the document classification system.

## Structure:
${documentTypes.map(type => `- ${type}/`).join('\n')}

## Usage:
1. Place document images in appropriate subdirectories
2. Run the training script: \`node scripts/trainModel.js\`
3. Select option 3 to upload training data
4. Start training with option 6

## Requirements:
- At least 10 samples per document type for basic training
- 100+ samples per type recommended for production use
- Balance dataset across all document types

## Supported Document Types:
${documentTypes.map(type => `- **${type.replace('_', ' ').toUpperCase()}**: ${getDocumentDescription(type)}`).join('\n')}
`;
    
    fs.writeFileSync(mainReadmePath, mainReadmeContent);
    
    console.log('✅ Sample data structure created');
    console.log(`📁 Location: ${sampleDataPath}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error creating sample data structure:', error.message);
    return false;
  }
}

/**
 * Get document type description
 */
function getDocumentDescription(docType) {
  const descriptions = {
    'aadhar': 'Indian national identity card',
    'voter_id': 'Election commission voter identity card',
    'driving_license': 'Transport department driving license',
    'passport': 'International travel document',
    'pan_card': 'Permanent account number tax card',
    'bank_statement': 'Financial institution statement',
    'utility_bill': 'Service provider billing document',
    'unknown': 'Unidentified or other document types'
  };
  
  return descriptions[docType] || 'Government or official document';
}

/**
 * Create sample configuration files
 */
async function createConfigurationFiles() {
  try {
    console.log('⚙️  Creating configuration files...');
    
    // Training configuration
    const trainingConfigPath = path.join(__dirname, '../config/training.json');
    const configDir = path.dirname(trainingConfigPath);
    
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    
    const trainingConfig = {
      default: {
        epochs: 50,
        batchSize: 32,
        learningRate: 0.001,
        validationSplit: 0.2,
        imageSize: [224, 224],
        numClasses: 8
      },
      development: {
        epochs: 10,
        batchSize: 16,
        learningRate: 0.001,
        validationSplit: 0.2
      },
      production: {
        epochs: 100,
        batchSize: 64,
        learningRate: 0.0005,
        validationSplit: 0.15
      }
    };
    
    fs.writeFileSync(trainingConfigPath, JSON.stringify(trainingConfig, null, 2));
    
    // Model configuration
    const modelConfigPath = path.join(__dirname, '../config/model.json');
    const modelConfig = {
      documentTypes: Object.values(documentClassification.DOCUMENT_TYPES),
      modelPaths: {
        classification: '../models/document_classification/classification',
        featureExtraction: '../models/document_classification/feature_extraction',
        validation: '../models/document_classification/validation'
      },
      preprocessing: {
        imageSize: [224, 224],
        normalization: true,
        augmentation: false
      },
      evaluation: {
        testSplit: 0.2,
        metrics: ['accuracy', 'precision', 'recall', 'f1Score'],
        saveResults: true
      }
    };
    
    fs.writeFileSync(modelConfigPath, JSON.stringify(modelConfig, null, 2));
    
    console.log('✅ Configuration files created');
    return true;
  } catch (error) {
    console.error('❌ Error creating configuration files:', error.message);
    return false;
  }
}

/**
 * Run system tests
 */
async function runSystemTests() {
  try {
    console.log('🧪 Running system tests...');
    
    // Test 1: Model initialization
    console.log('  Testing model initialization...');
    const initSuccess = await documentClassification.initializeDocumentClassification();
    if (!initSuccess) {
      throw new Error('Model initialization failed');
    }
    console.log('  ✅ Model initialization test passed');
    
    // Test 2: Training manager
    console.log('  Testing training manager...');
    const session = trainingManager.initializeTrainingSession({
      epochs: 1,
      batchSize: 1
    });
    if (!session) {
      throw new Error('Training session initialization failed');
    }
    console.log('  ✅ Training manager test passed');
    
    // Test 3: Database connection
    console.log('  Testing database operations...');
    const testDoc = new DocumentClassification({
      originalFilename: 'test.jpg',
      filePath: '/tmp/test.jpg',
      fileSize: 1024,
      mimeType: 'image/jpeg',
      classificationResult: {
        documentType: 'aadhar',
        confidence: 0.95,
        probabilities: { aadhar: 0.95 }
      },
      validationResult: {
        isAuthentic: true,
        confidence: 0.9,
        authenticityScore: 0.9,
        riskFactors: []
      },
      processingTime: 100,
      isTrainingData: false
    });
    
    await testDoc.save();
    await DocumentClassification.deleteOne({ _id: testDoc._id });
    console.log('  ✅ Database operations test passed');
    
    // Test 4: Directory structure
    console.log('  Testing directory structure...');
    const requiredDirs = [
      trainingDataManager.TRAINING_DATA_ROOT,
      trainingDataManager.ORGANIZED_DATA_PATH,
      trainingDataManager.RAW_DATA_PATH,
      trainingDataManager.AUGMENTED_DATA_PATH
    ];
    
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        throw new Error(`Required directory missing: ${dir}`);
      }
    }
    console.log('  ✅ Directory structure test passed');
    
    console.log('✅ All system tests passed');
    return true;
  } catch (error) {
    console.error('❌ System tests failed:', error.message);
    return false;
  }
}

/**
 * Display setup summary
 */
function displaySetupSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('🎉 ML DOCUMENT CLASSIFICATION SYSTEM SETUP COMPLETE');
  console.log('='.repeat(60));
  console.log('\n📋 Next Steps:');
  console.log('1. Add training data to: server/sample_data/');
  console.log('2. Run training script: node scripts/trainModel.js');
  console.log('3. Start the server: npm run dev');
  console.log('4. Access training interface: http://localhost:5000/ml-training');
  console.log('5. Test classification: http://localhost:5000/document-tester');
  
  console.log('\n🔧 Available Scripts:');
  console.log('- node scripts/trainModel.js    # Interactive training');
  console.log('- node scripts/setupMLSystem.js # System setup');
  
  console.log('\n📚 Documentation:');
  console.log('- ML_TRAINING_GUIDE.md          # Complete training guide');
  console.log('- server/sample_data/README.md  # Data preparation guide');
  
  console.log('\n🎯 Supported Document Types:');
  Object.values(documentClassification.DOCUMENT_TYPES).forEach(type => {
    console.log(`- ${type.replace('_', ' ').toUpperCase()}`);
  });
  
  console.log('\n' + '='.repeat(60));
}

/**
 * Main setup function
 */
async function main() {
  console.log('🚀 Starting ML Document Classification System Setup...\n');
  
  let success = true;
  
  // Step 1: Connect to database
  if (SETUP_CONFIG.setupDatabase) {
    const dbConnected = await connectToDatabase();
    if (!dbConnected) {
      success = false;
    } else {
      await initializeDatabase();
    }
  }
  
  // Step 2: Initialize ML system
  if (SETUP_CONFIG.initializeModels && success) {
    const mlInitialized = await initializeMLSystem();
    if (!mlInitialized) {
      success = false;
    }
  }
  
  // Step 3: Create sample data structure
  if (SETUP_CONFIG.createSampleData && success) {
    const sampleDataCreated = await createSampleDataStructure();
    if (!sampleDataCreated) {
      success = false;
    }
  }
  
  // Step 4: Create configuration files
  if (success) {
    const configCreated = await createConfigurationFiles();
    if (!configCreated) {
      success = false;
    }
  }
  
  // Step 5: Run tests
  if (SETUP_CONFIG.runTests && success) {
    const testsPass = await runSystemTests();
    if (!testsPass) {
      success = false;
    }
  }
  
  // Close database connection
  if (mongoose.connection.readyState === 1) {
    await mongoose.connection.close();
  }
  
  if (success) {
    displaySetupSummary();
    console.log('\n✅ Setup completed successfully!');
    process.exit(0);
  } else {
    console.log('\n❌ Setup failed. Please check the errors above.');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n👋 Setup interrupted');
  if (mongoose.connection.readyState === 1) {
    await mongoose.connection.close();
  }
  process.exit(0);
});

// Start setup if run directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal setup error:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  connectToDatabase,
  initializeDatabase,
  initializeMLSystem,
  createSampleDataStructure,
  createConfigurationFiles,
  runSystemTests
};
