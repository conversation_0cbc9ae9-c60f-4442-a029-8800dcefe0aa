#!/usr/bin/env node

/**
 * Model Training Script
 * 
 * This script provides a command-line interface for training the document
 * classification model with sample data or custom datasets.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const documentClassification = require('../ai/documentClassification');
const trainingManager = require('../ai/trainingManager');
const trainingDataManager = require('../ai/trainingDataManager');

// Command line interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Training configuration
let trainingConfig = {
  epochs: 50,
  batchSize: 32,
  learningRate: 0.001,
  validationSplit: 0.2
};

/**
 * Display welcome message and menu
 */
function displayMenu() {
  console.log('\n' + '='.repeat(60));
  console.log('🤖 ML Document Classification Training System');
  console.log('='.repeat(60));
  console.log('1. Initialize system');
  console.log('2. Create sample training data');
  console.log('3. Upload training data from directory');
  console.log('4. View training data statistics');
  console.log('5. Configure training parameters');
  console.log('6. Start model training');
  console.log('7. Test trained model');
  console.log('8. View training history');
  console.log('9. Clean up old data');
  console.log('0. Exit');
  console.log('='.repeat(60));
}

/**
 * Get user input
 */
function getUserInput(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * Initialize the ML system
 */
async function initializeSystem() {
  console.log('\n🔧 Initializing ML Document Classification System...');
  
  try {
    // Initialize document classification
    const success = await documentClassification.initializeDocumentClassification();
    
    if (success) {
      console.log('✅ Document classification system initialized successfully');
      
      // Initialize directory structure
      trainingDataManager.initializeDirectoryStructure();
      console.log('✅ Training data directories created');
      
      console.log('\n📁 Directory structure:');
      console.log(`   Training Data Root: ${trainingDataManager.TRAINING_DATA_ROOT}`);
      console.log(`   Organized Data: ${trainingDataManager.ORGANIZED_DATA_PATH}`);
      console.log(`   Raw Data: ${trainingDataManager.RAW_DATA_PATH}`);
      console.log(`   Augmented Data: ${trainingDataManager.AUGMENTED_DATA_PATH}`);
      
    } else {
      console.log('❌ Failed to initialize document classification system');
    }
  } catch (error) {
    console.error('❌ Error initializing system:', error.message);
  }
}

/**
 * Create sample training data
 */
async function createSampleData() {
  console.log('\n📝 Creating sample training data...');
  
  const sampleDataPath = path.join(__dirname, '../sample_data');
  
  if (!fs.existsSync(sampleDataPath)) {
    fs.mkdirSync(sampleDataPath, { recursive: true });
  }
  
  // Create sample data structure
  const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);
  
  documentTypes.forEach(docType => {
    const docTypePath = path.join(sampleDataPath, docType);
    if (!fs.existsSync(docTypePath)) {
      fs.mkdirSync(docTypePath, { recursive: true });
    }
  });
  
  console.log('✅ Sample data directory structure created');
  console.log(`📁 Sample data location: ${sampleDataPath}`);
  console.log('\n📋 Instructions:');
  console.log('1. Place your document images in the appropriate folders:');
  documentTypes.forEach(docType => {
    console.log(`   - ${docType}: ${path.join(sampleDataPath, docType)}`);
  });
  console.log('2. Use option 3 to upload the data for training');
  
  await getUserInput('\nPress Enter to continue...');
}

/**
 * Upload training data from directory
 */
async function uploadTrainingData() {
  console.log('\n📤 Upload Training Data');
  
  const dataPath = await getUserInput('Enter path to training data directory: ');
  
  if (!fs.existsSync(dataPath)) {
    console.log('❌ Directory does not exist');
    return;
  }
  
  console.log('\n🔍 Scanning directory for training data...');
  
  const trainingFiles = [];
  const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);
  
  // Scan for files in subdirectories
  documentTypes.forEach(docType => {
    const docTypePath = path.join(dataPath, docType);
    if (fs.existsSync(docTypePath)) {
      const files = fs.readdirSync(docTypePath);
      files.forEach(file => {
        const filePath = path.join(docTypePath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile() && file.match(/\.(jpg|jpeg|png|gif|bmp)$/i)) {
          trainingFiles.push({
            filePath: filePath,
            documentType: docType,
            split: 'train' // Default to training set
          });
        }
      });
    }
  });
  
  if (trainingFiles.length === 0) {
    console.log('❌ No training files found');
    return;
  }
  
  console.log(`📊 Found ${trainingFiles.length} training files`);
  
  // Show distribution
  const distribution = {};
  trainingFiles.forEach(file => {
    distribution[file.documentType] = (distribution[file.documentType] || 0) + 1;
  });
  
  console.log('\n📈 Distribution by document type:');
  Object.entries(distribution).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} files`);
  });
  
  const confirm = await getUserInput('\nProceed with upload? (y/n): ');
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ Upload cancelled');
    return;
  }
  
  console.log('\n⏳ Organizing training data...');
  
  try {
    const result = await trainingDataManager.organizeTrainingData(trainingFiles);
    
    if (result.success) {
      console.log(`✅ Successfully organized ${result.results.organized} files`);
      if (result.results.failed > 0) {
        console.log(`⚠️  Failed to organize ${result.results.failed} files`);
        result.results.errors.forEach(error => {
          console.log(`   - ${error.file}: ${error.error}`);
        });
      }
    } else {
      console.log(`❌ Failed to organize training data: ${result.error}`);
    }
  } catch (error) {
    console.error('❌ Error organizing training data:', error.message);
  }
}

/**
 * View training data statistics
 */
async function viewTrainingStats() {
  console.log('\n📊 Training Data Statistics');
  
  try {
    const stats = await trainingDataManager.getTrainingDataStats();
    
    if (stats.success) {
      console.log(`\n📈 Total Samples: ${stats.databaseStats.totalSamples}`);
      
      console.log('\n📋 By Document Type:');
      Object.entries(stats.databaseStats.byDocumentType).forEach(([type, count]) => {
        console.log(`   ${type}: ${count} samples`);
      });
      
      console.log('\n🗂️  By Dataset:');
      Object.entries(stats.databaseStats.byDataset).forEach(([dataset, count]) => {
        console.log(`   ${dataset}: ${count} samples`);
      });
      
      if (stats.databaseStats.qualityIssues.total > 0) {
        console.log('\n⚠️  Quality Issues:');
        console.log(`   Total files with issues: ${stats.databaseStats.qualityIssues.total}`);
        Object.entries(stats.databaseStats.qualityIssues.byType).forEach(([issue, count]) => {
          console.log(`   ${issue}: ${count} files`);
        });
      }
      
      console.log('\n💾 File System Stats:');
      Object.entries(stats.fileSystemStats).forEach(([docType, counts]) => {
        console.log(`   ${docType}:`);
        console.log(`     Training: ${counts.train}`);
        console.log(`     Validation: ${counts.validation}`);
        console.log(`     Test: ${counts.test}`);
      });
      
    } else {
      console.log(`❌ Failed to get training stats: ${stats.error}`);
    }
  } catch (error) {
    console.error('❌ Error getting training stats:', error.message);
  }
  
  await getUserInput('\nPress Enter to continue...');
}

/**
 * Configure training parameters
 */
async function configureTraining() {
  console.log('\n⚙️  Training Configuration');
  console.log('\nCurrent configuration:');
  console.log(`   Epochs: ${trainingConfig.epochs}`);
  console.log(`   Batch Size: ${trainingConfig.batchSize}`);
  console.log(`   Learning Rate: ${trainingConfig.learningRate}`);
  console.log(`   Validation Split: ${trainingConfig.validationSplit}`);
  
  const modify = await getUserInput('\nModify configuration? (y/n): ');
  if (modify.toLowerCase() !== 'y') {
    return;
  }
  
  const epochs = await getUserInput(`Epochs (${trainingConfig.epochs}): `);
  if (epochs) trainingConfig.epochs = parseInt(epochs);
  
  const batchSize = await getUserInput(`Batch Size (${trainingConfig.batchSize}): `);
  if (batchSize) trainingConfig.batchSize = parseInt(batchSize);
  
  const learningRate = await getUserInput(`Learning Rate (${trainingConfig.learningRate}): `);
  if (learningRate) trainingConfig.learningRate = parseFloat(learningRate);
  
  const validationSplit = await getUserInput(`Validation Split (${trainingConfig.validationSplit}): `);
  if (validationSplit) trainingConfig.validationSplit = parseFloat(validationSplit);
  
  console.log('\n✅ Configuration updated');
}

/**
 * Start model training
 */
async function startTraining() {
  console.log('\n🚀 Starting Model Training');
  
  try {
    // Initialize training session
    console.log('🔧 Initializing training session...');
    const session = trainingManager.initializeTrainingSession(trainingConfig);
    console.log(`✅ Training session initialized: ${session.id}`);
    
    // Prepare training dataset
    console.log('📊 Preparing training dataset...');
    const dataset = await trainingDataManager.prepareTrainingDataset();
    
    if (!dataset.success) {
      console.log(`❌ Failed to prepare dataset: ${dataset.error}`);
      return;
    }
    
    // Check if we have enough data
    const insufficientClasses = dataset.insufficientClasses;
    if (insufficientClasses.length > 0) {
      console.log(`⚠️  Warning: Insufficient data for classes: ${insufficientClasses.join(', ')}`);
      const proceed = await getUserInput('Continue anyway? (y/n): ');
      if (proceed.toLowerCase() !== 'y') {
        console.log('❌ Training cancelled');
        return;
      }
    }
    
    // Add training samples to session
    console.log('📤 Adding training samples to session...');
    const trainingFiles = [];
    
    Object.entries(dataset.dataset.training).forEach(([docType, docs]) => {
      docs.forEach(doc => {
        trainingFiles.push({
          path: doc.filePath,
          documentType: docType
        });
      });
    });
    
    if (trainingFiles.length === 0) {
      console.log('❌ No training files available');
      return;
    }
    
    const addResult = trainingManager.addTrainingSamples(
      trainingFiles.map(file => ({
        filePath: file.path,
        documentType: file.documentType
      })),
      'training'
    );
    
    if (!addResult.success) {
      console.log(`❌ Failed to add training samples: ${addResult.error}`);
      return;
    }
    
    console.log(`✅ Added ${addResult.samplesAdded} training samples`);
    
    // Start training
    console.log('\n🎯 Starting model training...');
    console.log('This may take several minutes depending on your hardware and dataset size.');
    
    const trainingResult = await trainingManager.startTraining();
    
    if (trainingResult.success) {
      console.log('\n🎉 Training completed successfully!');
      console.log(`📊 Final accuracy: ${(trainingResult.trainingStats.bestAccuracy * 100).toFixed(2)}%`);
      console.log(`⏱️  Training time: ${Math.round(trainingResult.trainingStats.trainingTime / 1000)}s`);
    } else {
      console.log(`❌ Training failed: ${trainingResult.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error during training:', error.message);
  }
  
  await getUserInput('\nPress Enter to continue...');
}

/**
 * Test trained model
 */
async function testModel() {
  console.log('\n🧪 Test Trained Model');
  
  const imagePath = await getUserInput('Enter path to test image: ');
  
  if (!fs.existsSync(imagePath)) {
    console.log('❌ Image file does not exist');
    return;
  }
  
  try {
    console.log('🔍 Classifying document...');
    
    const imageBuffer = fs.readFileSync(imagePath);
    const result = await documentClassification.classifyDocument(imageBuffer);
    
    if (result.success) {
      console.log('\n📋 Classification Results:');
      console.log(`   Document Type: ${result.documentType.toUpperCase()}`);
      console.log(`   Confidence: ${(result.confidence * 100).toFixed(2)}%`);
      
      console.log('\n📊 All Probabilities:');
      Object.entries(result.probabilities).forEach(([type, prob]) => {
        console.log(`   ${type}: ${(prob * 100).toFixed(2)}%`);
      });
      
      // Test authenticity
      console.log('\n🔒 Testing authenticity...');
      const authResult = await documentClassification.validateDocumentAuthenticity(imageBuffer);
      
      if (authResult.success) {
        console.log(`   Authentic: ${authResult.isAuthentic ? 'YES' : 'NO'}`);
        console.log(`   Authenticity Score: ${(authResult.authenticity_score * 100).toFixed(2)}%`);
        
        if (authResult.risk_factors && authResult.risk_factors.length > 0) {
          console.log('   Risk Factors:');
          authResult.risk_factors.forEach(factor => {
            console.log(`     - ${factor}`);
          });
        }
      }
      
    } else {
      console.log(`❌ Classification failed: ${result.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error testing model:', error.message);
  }
  
  await getUserInput('\nPress Enter to continue...');
}

/**
 * View training history
 */
async function viewTrainingHistory() {
  console.log('\n📚 Training History');
  
  const history = trainingManager.getTrainingHistory();
  
  if (history.length === 0) {
    console.log('No training history available');
  } else {
    console.log(`\n📊 Found ${history.length} training sessions:\n`);
    
    history.forEach((session, index) => {
      console.log(`${index + 1}. Session: ${session.id}`);
      console.log(`   Start Time: ${new Date(session.startTime).toLocaleString()}`);
      console.log(`   Status: ${session.status}`);
      console.log(`   Final Accuracy: ${session.finalAccuracy ? (session.finalAccuracy * 100).toFixed(2) + '%' : 'N/A'}`);
      console.log(`   Training Time: ${session.trainingTime ? Math.round(session.trainingTime / 1000) + 's' : 'N/A'}`);
      console.log(`   Sample Count: ${session.sampleCount || 'N/A'}`);
      console.log('');
    });
  }
  
  await getUserInput('Press Enter to continue...');
}

/**
 * Clean up old data
 */
async function cleanupOldData() {
  console.log('\n🧹 Clean Up Old Data');
  
  const days = await getUserInput('Remove training data older than how many days? (30): ');
  const daysOld = days ? parseInt(days) : 30;
  
  const confirm = await getUserInput(`Remove training data older than ${daysOld} days? (y/n): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ Cleanup cancelled');
    return;
  }
  
  try {
    console.log('🗑️  Cleaning up old training data...');
    
    const result = await trainingDataManager.cleanupOldTrainingFiles(daysOld);
    
    if (result.success) {
      console.log(`✅ Removed ${result.removedCount} old training files`);
    } else {
      console.log(`❌ Cleanup failed: ${result.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  }
  
  await getUserInput('\nPress Enter to continue...');
}

/**
 * Main application loop
 */
async function main() {
  console.log('🚀 Starting ML Document Classification Training System...');
  
  while (true) {
    displayMenu();
    
    const choice = await getUserInput('\nSelect an option (0-9): ');
    
    switch (choice) {
      case '1':
        await initializeSystem();
        break;
      case '2':
        await createSampleData();
        break;
      case '3':
        await uploadTrainingData();
        break;
      case '4':
        await viewTrainingStats();
        break;
      case '5':
        await configureTraining();
        break;
      case '6':
        await startTraining();
        break;
      case '7':
        await testModel();
        break;
      case '8':
        await viewTrainingHistory();
        break;
      case '9':
        await cleanupOldData();
        break;
      case '0':
        console.log('\n👋 Goodbye!');
        rl.close();
        process.exit(0);
        break;
      default:
        console.log('❌ Invalid option. Please try again.');
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Goodbye!');
  rl.close();
  process.exit(0);
});

// Start the application
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  initializeSystem,
  createSampleData,
  uploadTrainingData,
  viewTrainingStats,
  configureTraining,
  startTraining,
  testModel,
  viewTrainingHistory,
  cleanupOldData
};
