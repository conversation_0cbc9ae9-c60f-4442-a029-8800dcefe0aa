/**
 * Document Classification Model
 * 
 * This model stores document classification results, training data,
 * and model performance metrics.
 */

const mongoose = require('mongoose');

// Document Classification Result Schema
const documentClassificationSchema = new mongoose.Schema({
  // Basic information
  documentId: {
    type: String,
    required: true,
    unique: true,
    default: () => `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },
  
  // File information
  originalFilename: {
    type: String,
    required: true
  },
  filePath: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  mimeType: {
    type: String,
    required: true
  },
  
  // Classification results
  classificationResult: {
    documentType: {
      type: String,
      required: true,
      enum: ['aadhar', 'voter_id', 'driving_license', 'passport', 'pan_card', 'bank_statement', 'utility_bill', 'unknown']
    },
    confidence: {
      type: Number,
      required: true,
      min: 0,
      max: 1
    },
    probabilities: {
      aadhar: { type: Number, default: 0 },
      voter_id: { type: Number, default: 0 },
      driving_license: { type: Number, default: 0 },
      passport: { type: Number, default: 0 },
      pan_card: { type: Number, default: 0 },
      bank_statement: { type: Number, default: 0 },
      utility_bill: { type: Number, default: 0 },
      unknown: { type: Number, default: 0 }
    }
  },
  
  // Extracted features
  extractedFeatures: {
    visualFeatures: [Number],
    textFeatures: {
      extractedText: String,
      hasAadharNumber: Boolean,
      hasVoterIdNumber: Boolean,
      hasDrivingLicenseNumber: Boolean,
      hasPanNumber: Boolean,
      hasPassportNumber: Boolean,
      hasDatePattern: Boolean,
      hasAddressKeywords: Boolean,
      hasGovernmentKeywords: Boolean,
      wordCount: Number,
      digitCount: Number,
      upperCaseCount: Number
    },
    layoutFeatures: {
      width: Number,
      height: Number,
      aspectRatio: Number,
      brightness: Number,
      contrast: Number,
      textDensity: Number,
      edgeDensity: Number
    }
  },
  
  // Validation results
  validationResult: {
    isAuthentic: {
      type: Boolean,
      required: true
    },
    confidence: {
      type: Number,
      required: true,
      min: 0,
      max: 1
    },
    authenticityScore: {
      type: Number,
      required: true,
      min: 0,
      max: 1
    },
    riskFactors: [String]
  },
  
  // Processing metadata
  processingTime: {
    type: Number, // in milliseconds
    required: true
  },
  modelVersion: {
    type: String,
    default: '1.0.0'
  },
  
  // User and session information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Voter'
  },
  sessionId: String,
  ipAddress: String,
  userAgent: String,
  
  // Manual verification (for training data)
  manualVerification: {
    isVerified: {
      type: Boolean,
      default: false
    },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin'
    },
    verifiedAt: Date,
    actualDocumentType: {
      type: String,
      enum: ['aadhar', 'voter_id', 'driving_license', 'passport', 'pan_card', 'bank_statement', 'utility_bill', 'unknown']
    },
    isAuthentic: Boolean,
    verificationNotes: String
  },
  
  // Training data flags
  isTrainingData: {
    type: Boolean,
    default: false
  },
  trainingDataset: {
    type: String,
    enum: ['training', 'validation', 'test'],
    default: 'training'
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Training Session Schema
const trainingSessionSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    unique: true
  },
  
  // Session configuration
  config: {
    epochs: { type: Number, default: 50 },
    batchSize: { type: Number, default: 32 },
    learningRate: { type: Number, default: 0.001 },
    validationSplit: { type: Number, default: 0.2 },
    imageSize: { type: [Number], default: [224, 224] },
    numClasses: { type: Number, default: 8 }
  },
  
  // Training progress
  progress: {
    currentEpoch: { type: Number, default: 0 },
    totalEpochs: { type: Number, default: 50 },
    currentLoss: Number,
    currentAccuracy: Number,
    bestAccuracy: { type: Number, default: 0 },
    trainingTime: Number, // in milliseconds
    trainingStartTime: Date,
    status: {
      type: String,
      enum: ['initialized', 'training', 'completed', 'failed', 'cancelled'],
      default: 'initialized'
    }
  },
  
  // Dataset statistics
  datasetStats: {
    training: { type: Number, default: 0 },
    validation: { type: Number, default: 0 },
    test: { type: Number, default: 0 }
  },
  
  // Training metrics
  metrics: {
    loss: [Number],
    accuracy: [Number],
    valLoss: [Number],
    valAccuracy: [Number]
  },
  
  // Model information
  modelInfo: {
    modelPath: String,
    modelSize: Number,
    modelVersion: String,
    backupPath: String
  },
  
  // Training results
  finalResults: {
    finalLoss: Number,
    finalAccuracy: Number,
    finalValLoss: Number,
    finalValAccuracy: Number,
    trainingDuration: Number,
    samplesProcessed: Number
  },
  
  // Error information
  error: {
    message: String,
    stack: String,
    timestamp: Date
  },
  
  // User information
  initiatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  
  // Timestamps
  startTime: {
    type: Date,
    default: Date.now
  },
  endTime: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Model Performance Schema
const modelPerformanceSchema = new mongoose.Schema({
  modelVersion: {
    type: String,
    required: true
  },
  
  // Performance metrics
  metrics: {
    accuracy: { type: Number, required: true },
    precision: { type: Number, required: true },
    recall: { type: Number, required: true },
    f1Score: { type: Number, required: true },
    confusionMatrix: [[Number]], // 2D array
    classificationReport: mongoose.Schema.Types.Mixed
  },
  
  // Per-class metrics
  classMetrics: {
    aadhar: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    voter_id: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    driving_license: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    passport: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    pan_card: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    bank_statement: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    utility_bill: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    },
    unknown: {
      precision: Number,
      recall: Number,
      f1Score: Number,
      support: Number
    }
  },
  
  // Test dataset information
  testDataset: {
    totalSamples: Number,
    samplesPerClass: mongoose.Schema.Types.Mixed,
    testDate: Date
  },
  
  // Model information
  modelInfo: {
    trainingSessionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TrainingSession'
    },
    modelPath: String,
    modelSize: Number,
    parameters: Number
  },
  
  // Timestamps
  evaluatedAt: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better performance
documentClassificationSchema.index({ documentId: 1 });
documentClassificationSchema.index({ 'classificationResult.documentType': 1 });
documentClassificationSchema.index({ createdAt: -1 });
documentClassificationSchema.index({ userId: 1 });
documentClassificationSchema.index({ isTrainingData: 1 });

trainingSessionSchema.index({ sessionId: 1 });
trainingSessionSchema.index({ 'progress.status': 1 });
trainingSessionSchema.index({ startTime: -1 });
trainingSessionSchema.index({ initiatedBy: 1 });

modelPerformanceSchema.index({ modelVersion: 1 });
modelPerformanceSchema.index({ evaluatedAt: -1 });
modelPerformanceSchema.index({ 'metrics.accuracy': -1 });

// Update timestamps on save
documentClassificationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

trainingSessionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create models
const DocumentClassification = mongoose.model('DocumentClassification', documentClassificationSchema);
const TrainingSession = mongoose.model('TrainingSession', trainingSessionSchema);
const ModelPerformance = mongoose.model('ModelPerformance', modelPerformanceSchema);

module.exports = {
  DocumentClassification,
  TrainingSession,
  ModelPerformance
};
