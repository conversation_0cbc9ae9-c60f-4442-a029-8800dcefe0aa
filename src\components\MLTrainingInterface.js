/**
 * ML Training Interface Component
 * 
 * This component provides a comprehensive interface for training document
 * classification models, including data upload, training monitoring, and
 * model evaluation.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Form,
  ProgressBar,
  Alert,
  Table,
  Modal,
  Tabs,
  Tab,
  Badge,
  Spinner
} from 'react-bootstrap';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const MLTrainingInterface = () => {
  // State management
  const [activeTab, setActiveTab] = useState('upload');
  const [trainingSession, setTrainingSession] = useState(null);
  const [trainingStats, setTrainingStats] = useState(null);
  const [trainingHistory, setTrainingHistory] = useState([]);
  const [isTraining, setIsTraining] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [alerts, setAlerts] = useState([]);
  
  // Upload state
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [documentTypes, setDocumentTypes] = useState([]);
  const [datasetType, setDatasetType] = useState('training');
  const [supportedTypes, setSupportedTypes] = useState([]);
  
  // Training configuration
  const [trainingConfig, setTrainingConfig] = useState({
    epochs: 50,
    batchSize: 32,
    learningRate: 0.001,
    validationSplit: 0.2
  });
  
  // Modal state
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  // Fetch supported document types on component mount
  useEffect(() => {
    fetchSupportedDocumentTypes();
    fetchTrainingStats();
    fetchTrainingHistory();
  }, []);

  // Poll training progress when training is active
  useEffect(() => {
    let interval;
    if (isTraining) {
      interval = setInterval(() => {
        fetchTrainingStats();
      }, 2000); // Poll every 2 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTraining]);

  const fetchSupportedDocumentTypes = async () => {
    try {
      const response = await fetch('/api/ml-documents/document-types');
      const data = await response.json();
      if (data.success) {
        setSupportedTypes(data.supportedTypes);
      }
    } catch (error) {
      addAlert('Error fetching document types', 'danger');
    }
  };

  const fetchTrainingStats = async () => {
    try {
      const response = await fetch('/api/ml-documents/training/session/stats');
      const data = await response.json();
      if (data.success) {
        setTrainingStats(data.stats);
        
        // Check if training is in progress
        if (data.stats.status === 'training') {
          setIsTraining(true);
        } else if (isTraining && (data.stats.status === 'completed' || data.stats.status === 'failed')) {
          setIsTraining(false);
          addAlert(
            `Training ${data.stats.status}!`,
            data.stats.status === 'completed' ? 'success' : 'danger'
          );
        }
      }
    } catch (error) {
      console.error('Error fetching training stats:', error);
    }
  };

  const fetchTrainingHistory = async () => {
    try {
      const response = await fetch('/api/ml-documents/training/history');
      const data = await response.json();
      if (data.success) {
        setTrainingHistory(data.history);
      }
    } catch (error) {
      console.error('Error fetching training history:', error);
    }
  };

  const addAlert = (message, variant = 'info') => {
    const alert = {
      id: Date.now(),
      message,
      variant
    };
    setAlerts(prev => [...prev, alert]);
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleFileSelection = (event) => {
    const files = Array.from(event.target.files);
    setSelectedFiles(files);
    
    // Initialize document types array with default values
    setDocumentTypes(new Array(files.length).fill(supportedTypes[0] || 'aadhar'));
  };

  const handleDocumentTypeChange = (index, type) => {
    const newTypes = [...documentTypes];
    newTypes[index] = type;
    setDocumentTypes(newTypes);
  };

  const uploadTrainingData = async () => {
    if (selectedFiles.length === 0) {
      addAlert('Please select files to upload', 'warning');
      return;
    }

    if (documentTypes.length !== selectedFiles.length) {
      addAlert('Please specify document type for each file', 'warning');
      return;
    }

    try {
      const formData = new FormData();
      
      // Add files
      selectedFiles.forEach(file => {
        formData.append('samples', file);
      });
      
      // Add document types and dataset type
      formData.append('documentTypes', JSON.stringify(documentTypes));
      formData.append('datasetType', datasetType);

      setUploadProgress(0);
      
      const response = await fetch('/api/ml-documents/training/samples/upload', {
        method: 'POST',
        body: formData,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(progress);
        }
      });

      const data = await response.json();
      
      if (data.success) {
        addAlert(`Successfully uploaded ${data.samplesAdded} training samples`, 'success');
        setSelectedFiles([]);
        setDocumentTypes([]);
        fetchTrainingStats();
      } else {
        addAlert(`Upload failed: ${data.error}`, 'danger');
      }
    } catch (error) {
      addAlert(`Upload error: ${error.message}`, 'danger');
    } finally {
      setUploadProgress(0);
    }
  };

  const initializeTrainingSession = async () => {
    try {
      const response = await fetch('/api/ml-documents/training/session/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ config: trainingConfig })
      });

      const data = await response.json();
      
      if (data.success) {
        setTrainingSession(data.session);
        addAlert('Training session initialized successfully', 'success');
        fetchTrainingStats();
      } else {
        addAlert(`Failed to initialize training session: ${data.error}`, 'danger');
      }
    } catch (error) {
      addAlert(`Error initializing training session: ${error.message}`, 'danger');
    }
  };

  const startTraining = async () => {
    try {
      setIsTraining(true);
      addAlert('Starting model training...', 'info');
      
      const response = await fetch('/api/ml-documents/training/start', {
        method: 'POST'
      });

      const data = await response.json();
      
      if (data.success) {
        addAlert('Training started successfully', 'success');
      } else {
        addAlert(`Training failed to start: ${data.error}`, 'danger');
        setIsTraining(false);
      }
    } catch (error) {
      addAlert(`Error starting training: ${error.message}`, 'danger');
      setIsTraining(false);
    }
  };

  const clearTrainingSession = async () => {
    try {
      const response = await fetch('/api/ml-documents/training/session/clear', {
        method: 'DELETE'
      });

      const data = await response.json();
      
      if (data.success) {
        setTrainingSession(null);
        setTrainingStats(null);
        addAlert('Training session cleared', 'success');
      } else {
        addAlert(`Failed to clear session: ${data.error}`, 'danger');
      }
    } catch (error) {
      addAlert(`Error clearing session: ${error.message}`, 'danger');
    }
  };

  // Chart data for training progress
  const getTrainingProgressData = () => {
    if (!trainingStats?.metrics) return null;

    return {
      labels: trainingStats.metrics.loss?.map((_, index) => `Epoch ${index + 1}`) || [],
      datasets: [
        {
          label: 'Training Loss',
          data: trainingStats.metrics.loss || [],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          tension: 0.1
        },
        {
          label: 'Training Accuracy',
          data: trainingStats.metrics.accuracy || [],
          borderColor: 'rgb(54, 162, 235)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          tension: 0.1,
          yAxisID: 'y1'
        }
      ]
    };
  };

  // Chart data for dataset distribution
  const getDatasetDistributionData = () => {
    if (!trainingStats?.documentTypeDistribution) return null;

    const labels = Object.keys(trainingStats.documentTypeDistribution);
    const data = Object.values(trainingStats.documentTypeDistribution);

    return {
      labels: labels.map(label => label.replace('_', ' ').toUpperCase()),
      datasets: [{
        data: data,
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#FF6384',
          '#C9CBCF'
        ]
      }]
    };
  };

  return (
    <Container fluid className="ml-training-interface">
      {/* Alerts */}
      {alerts.map(alert => (
        <Alert key={alert.id} variant={alert.variant} className="mb-3">
          {alert.message}
        </Alert>
      ))}

      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h4>ML Document Classification Training</h4>
            </Card.Header>
            <Card.Body>
              <Tabs activeKey={activeTab} onSelect={setActiveTab}>
                {/* Data Upload Tab */}
                <Tab eventKey="upload" title="Upload Training Data">
                  <div className="mt-3">
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Dataset Type</Form.Label>
                          <Form.Select
                            value={datasetType}
                            onChange={(e) => setDatasetType(e.target.value)}
                          >
                            <option value="training">Training</option>
                            <option value="validation">Validation</option>
                            <option value="test">Test</option>
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Select Images</Form.Label>
                          <Form.Control
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileSelection}
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {selectedFiles.length > 0 && (
                      <div className="mb-3">
                        <h6>Selected Files ({selectedFiles.length})</h6>
                        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                          {selectedFiles.map((file, index) => (
                            <Row key={index} className="mb-2 align-items-center">
                              <Col md={6}>
                                <small>{file.name}</small>
                              </Col>
                              <Col md={4}>
                                <Form.Select
                                  size="sm"
                                  value={documentTypes[index] || ''}
                                  onChange={(e) => handleDocumentTypeChange(index, e.target.value)}
                                >
                                  {supportedTypes.map(type => (
                                    <option key={type} value={type}>
                                      {type.replace('_', ' ').toUpperCase()}
                                    </option>
                                  ))}
                                </Form.Select>
                              </Col>
                              <Col md={2}>
                                <Badge bg="secondary">{Math.round(file.size / 1024)}KB</Badge>
                              </Col>
                            </Row>
                          ))}
                        </div>
                      </div>
                    )}

                    {uploadProgress > 0 && (
                      <ProgressBar 
                        now={uploadProgress} 
                        label={`${uploadProgress}%`}
                        className="mb-3"
                      />
                    )}

                    <Button
                      variant="primary"
                      onClick={uploadTrainingData}
                      disabled={selectedFiles.length === 0 || uploadProgress > 0}
                    >
                      {uploadProgress > 0 ? 'Uploading...' : 'Upload Training Data'}
                    </Button>
                  </div>
                </Tab>

                {/* Training Tab */}
                <Tab eventKey="training" title="Model Training">
                  <div className="mt-3">
                    <Row>
                      <Col md={6}>
                        <Card>
                          <Card.Header>Training Configuration</Card.Header>
                          <Card.Body>
                            <Form.Group className="mb-3">
                              <Form.Label>Epochs</Form.Label>
                              <Form.Control
                                type="number"
                                value={trainingConfig.epochs}
                                onChange={(e) => setTrainingConfig({
                                  ...trainingConfig,
                                  epochs: parseInt(e.target.value)
                                })}
                              />
                            </Form.Group>
                            <Form.Group className="mb-3">
                              <Form.Label>Batch Size</Form.Label>
                              <Form.Control
                                type="number"
                                value={trainingConfig.batchSize}
                                onChange={(e) => setTrainingConfig({
                                  ...trainingConfig,
                                  batchSize: parseInt(e.target.value)
                                })}
                              />
                            </Form.Group>
                            <Form.Group className="mb-3">
                              <Form.Label>Learning Rate</Form.Label>
                              <Form.Control
                                type="number"
                                step="0.0001"
                                value={trainingConfig.learningRate}
                                onChange={(e) => setTrainingConfig({
                                  ...trainingConfig,
                                  learningRate: parseFloat(e.target.value)
                                })}
                              />
                            </Form.Group>
                            <Form.Group className="mb-3">
                              <Form.Label>Validation Split</Form.Label>
                              <Form.Control
                                type="number"
                                step="0.1"
                                min="0.1"
                                max="0.5"
                                value={trainingConfig.validationSplit}
                                onChange={(e) => setTrainingConfig({
                                  ...trainingConfig,
                                  validationSplit: parseFloat(e.target.value)
                                })}
                              />
                            </Form.Group>
                          </Card.Body>
                        </Card>
                      </Col>
                      <Col md={6}>
                        <Card>
                          <Card.Header>Training Status</Card.Header>
                          <Card.Body>
                            {trainingStats ? (
                              <div>
                                <p><strong>Status:</strong> 
                                  <Badge 
                                    bg={trainingStats.status === 'completed' ? 'success' : 
                                        trainingStats.status === 'training' ? 'warning' : 
                                        trainingStats.status === 'failed' ? 'danger' : 'secondary'}
                                    className="ms-2"
                                  >
                                    {trainingStats.status}
                                  </Badge>
                                </p>
                                <p><strong>Training Samples:</strong> {trainingStats.datasetStats?.training || 0}</p>
                                <p><strong>Validation Samples:</strong> {trainingStats.datasetStats?.validation || 0}</p>
                                <p><strong>Test Samples:</strong> {trainingStats.datasetStats?.test || 0}</p>
                                
                                {trainingStats.progress && (
                                  <div>
                                    <p><strong>Progress:</strong> {trainingStats.progress.currentEpoch}/{trainingStats.progress.totalEpochs} epochs</p>
                                    {trainingStats.progress.currentAccuracy && (
                                      <p><strong>Current Accuracy:</strong> {(trainingStats.progress.currentAccuracy * 100).toFixed(2)}%</p>
                                    )}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <p>No active training session</p>
                            )}
                          </Card.Body>
                        </Card>
                      </Col>
                    </Row>

                    <div className="mt-3">
                      <Button
                        variant="success"
                        onClick={initializeTrainingSession}
                        disabled={isTraining}
                        className="me-2"
                      >
                        Initialize Session
                      </Button>
                      <Button
                        variant="primary"
                        onClick={startTraining}
                        disabled={!trainingStats || isTraining || trainingStats.datasetStats?.training === 0}
                        className="me-2"
                      >
                        {isTraining ? (
                          <>
                            <Spinner size="sm" className="me-2" />
                            Training...
                          </>
                        ) : (
                          'Start Training'
                        )}
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={clearTrainingSession}
                        disabled={isTraining}
                        className="me-2"
                      >
                        Clear Session
                      </Button>
                      <Button
                        variant="info"
                        onClick={() => setShowHistoryModal(true)}
                      >
                        View History
                      </Button>
                    </div>
                  </div>
                </Tab>

                {/* Monitoring Tab */}
                <Tab eventKey="monitoring" title="Training Monitoring">
                  <div className="mt-3">
                    <Row>
                      <Col md={8}>
                        {getTrainingProgressData() && (
                          <Card>
                            <Card.Header>Training Progress</Card.Header>
                            <Card.Body>
                              <Line
                                data={getTrainingProgressData()}
                                options={{
                                  responsive: true,
                                  scales: {
                                    y: {
                                      type: 'linear',
                                      display: true,
                                      position: 'left',
                                    },
                                    y1: {
                                      type: 'linear',
                                      display: true,
                                      position: 'right',
                                      grid: {
                                        drawOnChartArea: false,
                                      },
                                    },
                                  }
                                }}
                              />
                            </Card.Body>
                          </Card>
                        )}
                      </Col>
                      <Col md={4}>
                        {getDatasetDistributionData() && (
                          <Card>
                            <Card.Header>Dataset Distribution</Card.Header>
                            <Card.Body>
                              <Doughnut
                                data={getDatasetDistributionData()}
                                options={{
                                  responsive: true,
                                  maintainAspectRatio: false
                                }}
                                height={200}
                              />
                            </Card.Body>
                          </Card>
                        )}
                      </Col>
                    </Row>
                  </div>
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Training History Modal */}
      <Modal show={showHistoryModal} onHide={() => setShowHistoryModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Training History</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {trainingHistory.length > 0 ? (
            <Table striped bordered hover>
              <thead>
                <tr>
                  <th>Session ID</th>
                  <th>Start Time</th>
                  <th>Status</th>
                  <th>Final Accuracy</th>
                  <th>Duration</th>
                  <th>Samples</th>
                </tr>
              </thead>
              <tbody>
                {trainingHistory.map((session, index) => (
                  <tr key={index}>
                    <td>{session.sessionId || session.id}</td>
                    <td>{new Date(session.startTime).toLocaleString()}</td>
                    <td>
                      <Badge bg={session.status === 'completed' ? 'success' : 'danger'}>
                        {session.status}
                      </Badge>
                    </td>
                    <td>{session.finalAccuracy ? (session.finalAccuracy * 100).toFixed(2) + '%' : 'N/A'}</td>
                    <td>{session.trainingTime ? Math.round(session.trainingTime / 1000) + 's' : 'N/A'}</td>
                    <td>{session.sampleCount || 'N/A'}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <p>No training history available.</p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowHistoryModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default MLTrainingInterface;
