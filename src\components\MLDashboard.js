/**
 * ML Dashboard Component
 * 
 * This component provides a comprehensive dashboard for monitoring
 * the ML document classification system performance and statistics.
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Table,
  Badge,
  Alert,
  Tabs,
  Tab,
  Modal,
  ProgressBar
} from 'react-bootstrap';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import MLTrainingInterface from './MLTrainingInterface';
import DocumentTester from './DocumentTester';

const MLDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [systemStats, setSystemStats] = useState(null);
  const [performanceHistory, setPerformanceHistory] = useState([]);
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [evaluationResults, setEvaluationResults] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);

  useEffect(() => {
    fetchSystemStats();
    fetchPerformanceHistory();
  }, []);

  const addAlert = (message, variant = 'info') => {
    const alert = {
      id: Date.now(),
      message,
      variant
    };
    setAlerts(prev => [...prev, alert]);
    
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const fetchSystemStats = async () => {
    try {
      const response = await fetch('/api/ml-documents/training/session/stats');
      const data = await response.json();
      if (data.success) {
        setSystemStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching system stats:', error);
    }
  };

  const fetchPerformanceHistory = async () => {
    try {
      const response = await fetch('/api/ml-documents/performance/history');
      const data = await response.json();
      if (data.success) {
        setPerformanceHistory(data.history);
      }
    } catch (error) {
      console.error('Error fetching performance history:', error);
    }
  };

  const runModelEvaluation = async () => {
    setIsEvaluating(true);
    try {
      const response = await fetch('/api/ml-documents/evaluate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          modelVersion: '1.0.0',
          useStoredTestData: true
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setEvaluationResults(data.evaluation);
        setShowEvaluationModal(true);
        addAlert('Model evaluation completed successfully!', 'success');
        fetchPerformanceHistory(); // Refresh history
      } else {
        addAlert(`Evaluation failed: ${data.error}`, 'danger');
      }
    } catch (error) {
      addAlert(`Error during evaluation: ${error.message}`, 'danger');
    } finally {
      setIsEvaluating(false);
    }
  };

  // Chart data for performance history
  const getPerformanceHistoryData = () => {
    if (performanceHistory.length === 0) return null;

    return {
      labels: performanceHistory.map((_, index) => `Model ${index + 1}`),
      datasets: [
        {
          label: 'Accuracy',
          data: performanceHistory.map(p => p.metrics.accuracy * 100),
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        },
        {
          label: 'F1-Score',
          data: performanceHistory.map(p => p.metrics.f1Score * 100),
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          tension: 0.1
        }
      ]
    };
  };

  // Chart data for class performance
  const getClassPerformanceData = () => {
    if (!evaluationResults?.perClassMetrics) return null;

    const classes = Object.keys(evaluationResults.perClassMetrics);
    const precisionData = classes.map(cls => evaluationResults.perClassMetrics[cls].precision * 100);
    const recallData = classes.map(cls => evaluationResults.perClassMetrics[cls].recall * 100);
    const f1Data = classes.map(cls => evaluationResults.perClassMetrics[cls].f1Score * 100);

    return {
      labels: classes.map(cls => cls.replace('_', ' ').toUpperCase()),
      datasets: [
        {
          label: 'Precision',
          data: precisionData,
          backgroundColor: 'rgba(54, 162, 235, 0.6)'
        },
        {
          label: 'Recall',
          data: recallData,
          backgroundColor: 'rgba(255, 206, 86, 0.6)'
        },
        {
          label: 'F1-Score',
          data: f1Data,
          backgroundColor: 'rgba(75, 192, 192, 0.6)'
        }
      ]
    };
  };

  return (
    <Container fluid className="ml-dashboard">
      {/* Alerts */}
      {alerts.map(alert => (
        <Alert key={alert.id} variant={alert.variant} className="mb-3">
          {alert.message}
        </Alert>
      ))}

      <Row className="mb-4">
        <Col>
          <h2>🤖 ML Document Classification Dashboard</h2>
          <p className="text-muted">
            Monitor and manage your document classification system
          </p>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-4">
        {/* Overview Tab */}
        <Tab eventKey="overview" title="📊 Overview">
          <Row>
            {/* System Status Cards */}
            <Col md={3}>
              <Card className="mb-3">
                <Card.Body className="text-center">
                  <h5>System Status</h5>
                  <Badge 
                    bg={systemStats?.status === 'ready' ? 'success' : 'warning'}
                    style={{ fontSize: '1rem', padding: '0.5rem 1rem' }}
                  >
                    {systemStats?.status || 'Unknown'}
                  </Badge>
                </Card.Body>
              </Card>
            </Col>
            
            <Col md={3}>
              <Card className="mb-3">
                <Card.Body className="text-center">
                  <h5>Training Data</h5>
                  <h3 className="text-primary">
                    {systemStats?.datasetStats?.training || 0}
                  </h3>
                  <small className="text-muted">samples</small>
                </Card.Body>
              </Card>
            </Col>
            
            <Col md={3}>
              <Card className="mb-3">
                <Card.Body className="text-center">
                  <h5>Validation Data</h5>
                  <h3 className="text-info">
                    {systemStats?.datasetStats?.validation || 0}
                  </h3>
                  <small className="text-muted">samples</small>
                </Card.Body>
              </Card>
            </Col>
            
            <Col md={3}>
              <Card className="mb-3">
                <Card.Body className="text-center">
                  <h5>Test Data</h5>
                  <h3 className="text-warning">
                    {systemStats?.datasetStats?.test || 0}
                  </h3>
                  <small className="text-muted">samples</small>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Row>
            {/* Performance History Chart */}
            <Col md={8}>
              <Card>
                <Card.Header>
                  <div className="d-flex justify-content-between align-items-center">
                    <h5>Model Performance History</h5>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={runModelEvaluation}
                      disabled={isEvaluating}
                    >
                      {isEvaluating ? 'Evaluating...' : 'Run Evaluation'}
                    </Button>
                  </div>
                </Card.Header>
                <Card.Body>
                  {getPerformanceHistoryData() ? (
                    <Line
                      data={getPerformanceHistoryData()}
                      options={{
                        responsive: true,
                        scales: {
                          y: {
                            beginAtZero: true,
                            max: 100
                          }
                        }
                      }}
                    />
                  ) : (
                    <div className="text-center text-muted py-5">
                      <p>No performance history available</p>
                      <Button variant="outline-primary" onClick={runModelEvaluation}>
                        Run First Evaluation
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>

            {/* Quick Actions */}
            <Col md={4}>
              <Card>
                <Card.Header>
                  <h5>Quick Actions</h5>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Button
                      variant="success"
                      onClick={() => setActiveTab('training')}
                    >
                      🎯 Start Training
                    </Button>
                    <Button
                      variant="info"
                      onClick={() => setActiveTab('testing')}
                    >
                      🧪 Test Model
                    </Button>
                    <Button
                      variant="warning"
                      onClick={runModelEvaluation}
                      disabled={isEvaluating}
                    >
                      📊 Evaluate Model
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={() => setActiveTab('performance')}
                    >
                      📈 View Performance
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        {/* Training Tab */}
        <Tab eventKey="training" title="🎯 Training">
          <MLTrainingInterface />
        </Tab>

        {/* Testing Tab */}
        <Tab eventKey="testing" title="🧪 Testing">
          <DocumentTester />
        </Tab>

        {/* Performance Tab */}
        <Tab eventKey="performance" title="📈 Performance">
          <Row>
            <Col>
              <Card>
                <Card.Header>
                  <h5>Performance History</h5>
                </Card.Header>
                <Card.Body>
                  {performanceHistory.length > 0 ? (
                    <Table striped bordered hover>
                      <thead>
                        <tr>
                          <th>Model Version</th>
                          <th>Evaluation Date</th>
                          <th>Accuracy</th>
                          <th>Precision</th>
                          <th>Recall</th>
                          <th>F1-Score</th>
                          <th>Test Samples</th>
                        </tr>
                      </thead>
                      <tbody>
                        {performanceHistory.map((perf, index) => (
                          <tr key={index}>
                            <td>{perf.modelVersion}</td>
                            <td>{new Date(perf.evaluatedAt).toLocaleDateString()}</td>
                            <td>
                              <Badge bg="success">
                                {(perf.metrics.accuracy * 100).toFixed(2)}%
                              </Badge>
                            </td>
                            <td>{(perf.metrics.precision * 100).toFixed(2)}%</td>
                            <td>{(perf.metrics.recall * 100).toFixed(2)}%</td>
                            <td>{(perf.metrics.f1Score * 100).toFixed(2)}%</td>
                            <td>{perf.testDataset.totalSamples}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <div className="text-center text-muted py-5">
                      <p>No performance data available</p>
                      <Button variant="primary" onClick={runModelEvaluation}>
                        Run Model Evaluation
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>
      </Tabs>

      {/* Evaluation Results Modal */}
      <Modal show={showEvaluationModal} onHide={() => setShowEvaluationModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Model Evaluation Results</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {evaluationResults && (
            <Row>
              {/* Overall Metrics */}
              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>Overall Performance</Card.Header>
                  <Card.Body>
                    <Table>
                      <tbody>
                        <tr>
                          <td><strong>Accuracy</strong></td>
                          <td>
                            <Badge bg="success">
                              {(evaluationResults.overallMetrics.accuracy * 100).toFixed(2)}%
                            </Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Precision</strong></td>
                          <td>{(evaluationResults.overallMetrics.precision * 100).toFixed(2)}%</td>
                        </tr>
                        <tr>
                          <td><strong>Recall</strong></td>
                          <td>{(evaluationResults.overallMetrics.recall * 100).toFixed(2)}%</td>
                        </tr>
                        <tr>
                          <td><strong>F1-Score</strong></td>
                          <td>{(evaluationResults.overallMetrics.f1Score * 100).toFixed(2)}%</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>

              {/* Test Dataset Info */}
              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>Test Dataset</Card.Header>
                  <Card.Body>
                    <Table>
                      <tbody>
                        <tr>
                          <td><strong>Total Samples</strong></td>
                          <td>{evaluationResults.testDataset.totalSamples}</td>
                        </tr>
                        <tr>
                          <td><strong>Correct Predictions</strong></td>
                          <td className="text-success">{evaluationResults.summary.correctPredictions}</td>
                        </tr>
                        <tr>
                          <td><strong>Incorrect Predictions</strong></td>
                          <td className="text-danger">{evaluationResults.summary.incorrectPredictions}</td>
                        </tr>
                        <tr>
                          <td><strong>Avg. Confidence</strong></td>
                          <td>{(evaluationResults.summary.averageConfidence * 100).toFixed(2)}%</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>

              {/* Per-Class Performance */}
              <Col md={12}>
                <Card>
                  <Card.Header>Per-Class Performance</Card.Header>
                  <Card.Body>
                    {getClassPerformanceData() && (
                      <Bar
                        data={getClassPerformanceData()}
                        options={{
                          responsive: true,
                          scales: {
                            y: {
                              beginAtZero: true,
                              max: 100
                            }
                          }
                        }}
                      />
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEvaluationModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Loading overlay for evaluation */}
      {isEvaluating && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999
          }}
        >
          <Card style={{ padding: '2rem', textAlign: 'center' }}>
            <h5>Evaluating Model Performance...</h5>
            <ProgressBar animated now={100} className="mt-3" />
            <p className="mt-2 text-muted">This may take a few minutes</p>
          </Card>
        </div>
      )}
    </Container>
  );
};

export default MLDashboard;
